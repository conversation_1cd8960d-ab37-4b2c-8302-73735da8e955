import { describe, it, expect, vi, beforeEach } from 'vitest'
import { screen, fireEvent, waitFor } from '@testing-library/react'
import userEvent from '@testing-library/user-event'
import { render } from '@/test/utils'
import ScraperForm from '../ScraperForm'

// Mock the scraping library
vi.mock('@/lib/scraping', () => ({
  webScraper: {
    scrapeUrl: vi.fn().mockResolvedValue({
      url: 'https://example.com',
      title: 'Test Page',
      content: 'Test content',
      headings: ['Test Heading'],
      links: ['https://example.com/link'],
      wordCount: 10,
      metadata: {},
    }),
  },
}))

describe('ScraperForm', () => {
  beforeEach(() => {
    vi.clearAllMocks()
  })

  it('renders the form correctly', () => {
    render(<ScraperForm />)

    expect(screen.getByText('Web Scraper')).toBeInTheDocument()
    expect(screen.getByPlaceholderText('https://example.com')).toBeInTheDocument()
    expect(screen.getByRole('button', { name: /Start Scraping/i })).toBeInTheDocument()
  })

  it('validates URL input', async () => {
    const user = userEvent.setup()
    render(<ScraperForm />)

    const input = screen.getByPlaceholderText('https://example.com')
    const submitButton = screen.getByRole('button', { name: /Start Scraping/i })

    // Test with invalid URL
    await user.clear(input)
    await user.type(input, 'invalid-url')
    await user.click(submitButton)

    // The component should show some kind of validation error
    await waitFor(() => {
      // Look for any error-related text that might appear
      const errorElements = screen.queryAllByText(/invalid|error|warning|failed/i)
      expect(errorElements.length).toBeGreaterThan(0)
    })
  })

  it('accepts valid URLs', async () => {
    const user = userEvent.setup()
    render(<ScraperForm />)

    const input = screen.getByPlaceholderText('https://example.com')
    const submitButton = screen.getByRole('button', { name: /Start Scraping/i })

    // Test with valid URL - clear first since there might be a default value
    await user.clear(input)
    await user.type(input, 'https://example.com')
    await user.click(submitButton)

    await waitFor(() => {
      // Look for progress or scraping related text
      const progressElements = screen.queryAllByText(/scraping|progress|processing/i)
      expect(progressElements.length).toBeGreaterThan(0)
    })
  })

  it('handles multiple URLs', async () => {
    const user = userEvent.setup()
    render(<ScraperForm />)

    const input = screen.getByPlaceholderText('https://example.com')
    const addButton = screen.getByRole('button', { name: /Add URL/i })
    const submitButton = screen.getByRole('button', { name: /Start Scraping/i })

    // Add first URL
    await user.type(input, 'https://example.com')

    // Add second URL
    await user.click(addButton)
    const inputs = screen.getAllByPlaceholderText('https://example.com')
    await user.type(inputs[1], 'https://test.com')

    await user.click(submitButton)

    await waitFor(() => {
      expect(screen.getByText(/Scraping Progress/)).toBeInTheDocument()
    })
  })

  it('shows progress during scraping', async () => {
    const user = userEvent.setup()
    render(<ScraperForm />)

    const input = screen.getByPlaceholderText('https://example.com')
    const submitButton = screen.getByRole('button', { name: /Start Scraping/i })

    await user.clear(input)
    await user.type(input, 'https://example.com')
    await user.click(submitButton)

    // Should show loading state - button should be disabled during scraping
    await waitFor(() => {
      expect(submitButton).toBeDisabled()
    })
  })

  it('displays results after successful scraping', async () => {
    const user = userEvent.setup()
    render(<ScraperForm />)

    const input = screen.getByPlaceholderText('https://example.com')
    const submitButton = screen.getByRole('button', { name: /Start Scraping/i })

    await user.clear(input)
    await user.type(input, 'https://example.com')
    await user.click(submitButton)

    // Wait for scraping to complete and results to be displayed
    await waitFor(() => {
      // Look for any success indicators or result content
      const resultElements = screen.queryAllByText(/completed|success|result|scraped/i)
      expect(resultElements.length).toBeGreaterThan(0)
    }, { timeout: 5000 })
  })

  it('handles scraping errors gracefully', async () => {
    // Mock scraping failure
    const { webScraper } = await import('@/lib/scraping')
    vi.mocked(webScraper.scrapeUrl).mockResolvedValueOnce({
      url: 'https://example.com',
      error: 'Scraping failed'
    })

    const user = userEvent.setup()
    render(<ScraperForm />)

    const input = screen.getByPlaceholderText('https://example.com')
    const submitButton = screen.getByRole('button', { name: /Start Scraping/i })

    await user.clear(input)
    await user.type(input, 'https://example.com')
    await user.click(submitButton)

    await waitFor(() => {
      // Look for error indicators
      const errorElements = screen.queryAllByText(/failed|error|warning/i)
      expect(errorElements.length).toBeGreaterThan(0)
    }, { timeout: 5000 })
  })

  it('allows clearing the form', async () => {
    const user = userEvent.setup()
    render(<ScraperForm />)

    const input = screen.getByPlaceholderText('https://example.com')

    await user.type(input, 'https://example.com')
    expect(input).toHaveValue('https://example.com')

    // Clear the input manually
    await user.clear(input)
    expect(input).toHaveValue('')
  })

  it('prevents submission with empty input', async () => {
    const user = userEvent.setup()
    render(<ScraperForm />)

    const input = screen.getByPlaceholderText('https://example.com')
    const submitButton = screen.getByRole('button', { name: /Start Scraping/i })

    // Clear any default value and try to submit
    await user.clear(input)
    await user.click(submitButton)

    // Should show some validation message or prevent submission
    await waitFor(() => {
      // Look for validation messages or check that nothing happened
      const validationElements = screen.queryAllByText(/enter|url|required|empty/i)
      expect(validationElements.length).toBeGreaterThan(0)
    })
  })
})
